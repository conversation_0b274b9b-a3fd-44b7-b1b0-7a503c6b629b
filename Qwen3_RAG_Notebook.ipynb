{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# Qwen3 RAG System\n", "\n", "This notebook demonstrates a complete RAG (Retrieval-Augmented Generation) system using Qwen3 models:\n", "- **Qwen3-Embedding-0.6B** for document embeddings\n", "- **Qwen3-<PERSON><PERSON><PERSON>-0.6B** for document reranking\n", "- **Qwen3-4B** for answer generation\n", "- **FAISS** for efficient vector storage and retrieval"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## Setup and Installation"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "install", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "e8ce30d6-c838-4023-a2a3-5072d45c512c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: faiss-cpu in /usr/local/lib/python3.11/dist-packages (1.12.0)\n", "Requirement already satisfied: PyPDF2 in /usr/local/lib/python3.11/dist-packages (3.0.1)\n", "Requirement already satisfied: numpy<3.0,>=1.25.0 in /usr/local/lib/python3.11/dist-packages (from faiss-cpu) (2.0.2)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from faiss-cpu) (25.0)\n"]}], "source": ["# Install required packages\n", "!pip install faiss-cpu PyPDF2"]}, {"cell_type": "code", "source": ["!python qwen_rag.py"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "42Y-Mh_jGyTi", "outputId": "0fbafdba-4b07-4acf-c2e9-6c529b457a01"}, "execution_count": 7, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Using device: cuda\n", "=== Simple Qwen3 RAG System ===\n", "GPU Memory: 0.0GB\n", "\n", "Building vector store from Data folder...\n", "Processing Ultimate LangSmith Guide.pdf...\n", "Processing fine_tuning.txt...\n", "Processing Mistral Large 2_ Powerful Enough to Challenge Llama 3.1 405B_.pdf...\n", "Processing Build Large Language Models from Scratch.pdf...\n", "Processing Harness the Power of LLMs_ Zero-shot and Few-shot Prompting.pdf...\n", "Processing Parameter-Efficient Fine-Tuning of Large Language Models with LoRA and QLoRA.pdf...\n", "Processing What is Generative AI and How Does it Work_.pdf...\n", "Processing Understanding ChatGPT and Model Training in Simple Terms.pdf...\n", "Processing What is Lang<PERSON>hain_.pdf...\n", "Processing LLM Fine Tuning with PEFT Techniques.pdf...\n", "Processing What is Retrieval-Augmented Generation (RAG)_.pdf...\n", "Processing machine_learning.txt...\n", "Processing DataHour_ LlamaIndex QA System with Private Data and Effective Evaluation.pdf...\n", "Processing Implementing the Tree of Thoughts Method in AI.pdf...\n", "Processing Beyond the Buzz_ Exploring the Practical Applications of Generative AI in Industries.pdf...\n", "Processing Ludwig_ A Comprehensive Guide to LLM Fine Tuning using LoRA.pdf...\n", "Processing What is the Chain of Emotion in Prompt Engineering_.pdf...\n", "Processing Efficient LLM Workflows with LangChain Expression Language.pdf...\n", "Processing What is Prompt Engineering_.pdf...\n", "Processing Finetuning Large Language Models.pdf...\n", "Processing Building a Multi-Modal RAG Pipeline with Langchain.pdf...\n", "Processing What are Large Language Models(LLMs)_.pdf...\n", "Processing What is Skeleton of Thoughts and its Python Implementation_.pdf...\n", "Processing What is LangGraph_.pdf...\n", "Processing What is Responsible AI_.pdf...\n", "Processing Fine-tuning Google Gemma with Unsloth.pdf...\n", "Processing What is Chain-of-Thought Prompting and Its Benefits_.pdf...\n", "Processing rag_systems.txt...\n", "Processing Harnessing NLP Superpowers_ A Step-by-Step Hugging Face Fine Tuning Tutorial.pdf...\n", "Processing A Comprehensive Guide to Using Chains in Langchain.pdf...\n", "Processing transformers.txt...\n", "Processing Build a RAG Pipeline With the LLama Index.pdf...\n", "Processing Step-by-Step Guide to Training ML Model with No Code.pdf...\n", "Processed 119 document chunks\n", "Generating embeddings...\n", "Loading embedding model...\n", "2025-08-14 17:51:30.174643: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:467] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n", "WARNING: All log messages before absl::InitializeLog() is called are written to STDERR\n", "E0000 00:00:1755193890.204028    8171 cuda_dnn.cc:8579] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n", "E0000 00:00:1755193890.209770    8171 cuda_blas.cc:1407] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "W0000 00:00:1755193890.227347    8171 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.\n", "W0000 00:00:1755193890.227381    8171 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.\n", "W0000 00:00:1755193890.227387    8171 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.\n", "W0000 00:00:1755193890.227393    8171 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.\n", "2025-08-14 17:51:30.231798: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "Embedding model loaded!\n", "GPU Memory: 1.2GB\n", "Embedding model unloaded\n", "Creating vector store...\n", "Vector store saved to vector_store\n", "\n", "Query: What is the difference between LoRA and QLoRA?\n", "Searching documents...\n", "Loading embedding model...\n", "Embedding model loaded!\n", "GPU Memory: 1.2GB\n", "Embedding model unloaded\n", "Found 15 candidates\n", "Reranking documents...\n", "Loading reranker model...\n", "Reranker model loaded!\n", "GPU Memory: 1.2GB\n", "Reranker model unloaded\n", "Reranked to top 3 documents\n", "Generating answer...\n", "Loading instruct model...\n", "Loading checkpoint shards: 100% 3/3 [00:36<00:00, 12.24s/it]\n", "Instruct model loaded!\n", "GPU Memory: 8.1GB\n", "Instruct model unloaded\n", "\n", "Question: What is the difference between LoRA and QLoRA?\n", "Answer: LoRA (Low-Rank Adaptation) and QLoRA (Quantized Low-Rank Adaptation) are both parameter-efficient fine-tuning techniques used to adapt large language models (LLMs) to specific tasks. While they share similarities, they differ in their approach and efficiency.\n", "\n", "1. **LoRA (Low-Rank Adaptation)**:\n", "   - LoRA introduces low-rank matrices (A and B) into the self-attention module of each layer in the pre-trained model. These matrices act as adapters that allow the model to adapt and specialize for specific tasks while minimizing the number of additional parameters needed.\n", "   - LoRA reduces parameter overhead by focusing on optimizing trainable low-rank matrices instead of fine-tuning all parameters. This makes it much more memory-efficient and computationally cheaper.\n", "   - LoRA allows the pre-trained model to be shared across multiple tasks, facilitating efficient task-switching during deployment.\n", "   - LoRA does not introduce any additional inference latency compared to fully fine-tuned models, making it suitable for real-time applications.\n", "\n", "2. **QLoRA (Quantized Low-Rank Adaptation)**:\n", "   - QLoRA is an extension of LoRA that further introduces quantization to enhance parameter efficiency during fine-tuning. It builds on the principles of LoRA while introducing 4-bit NormalFloat (NF4) quantization and Double Quantization techniques.\n", "   - NF4 quantization leverages the inherent distribution of pre-trained neural network weights, transforming all weights to a fixed distribution that fits within the range of NF4 (-1 to 1). This allows for effective quantization without the need for expensive quantile estimation algorithms.\n", "   - Double Quantization addresses the memory overhead of quantization constants by quantizing the quantization constants themselves. This significantly reduces the memory footprint without compromising performance.\n", "   - QLoRA achieves even higher memory efficiency by introducing quantization, making it particularly valuable for deploying large models on resource-constrained devices.\n", "   - Despite its parameter-efficient nature, QLoRA retains high model quality, performing on par or even better than fully fine-tuned models on various downstream tasks.\n", "\n", "In summary, while LoRA focuses on reducing the number of trainable parameters through low-rank adaptation, QLoRA further enhances this efficiency by incorporating quantization techniques, making it more suitable for deployment on devices with limited computational resources.\n", "\n", "Sources: fine_tuning.txt, Parameter-Efficient Fine-Tuning of Large Language Models with LoRA and QLoRA.pdf, Parameter-Efficient Fine-Tuning of Large Language Models with LoRA and QLoRA.pdf\n", "\n", "=== RAG Query Completed ===\n", "GPU Memory: 0.0GB\n"]}]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}